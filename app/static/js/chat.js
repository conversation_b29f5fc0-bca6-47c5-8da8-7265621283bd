let isWaiting = false;
let sessionId = "default";

// Initialize chat when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    initializeSidebar();
});

function initializeChat() {
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const clearBtn = document.getElementById('clear-chat');

    // Initialize enhanced input features
    initializeEnhancedInput();
    initializeQuickActions();

    // Enable/disable send button based on input
    userInput.addEventListener('input', function() {
        const hasText = this.value.trim().length > 0;
        sendBtn.disabled = !hasText;
        updateSendButtonState(hasText);
        updateCharacterCount();
        autoResizeTextarea();
    });

    // Handle Enter key
    userInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && !isWaiting) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Handle send button click
    sendBtn.addEventListener('click', function() {
        if (!isWaiting) {
            sendMessage();
        }
    });

    // Handle clear chat button
    clearBtn.addEventListener('click', function() {
        clearChat();
    });

    // Initialize character count
    updateCharacterCount();
}

function initializeEnhancedInput() {
    const userInput = document.getElementById('user-input');

    // Auto-resize textarea
    function autoResizeTextarea() {
        userInput.style.height = 'auto';
        userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
    }

    // Set initial height
    autoResizeTextarea();

    // Handle paste events
    userInput.addEventListener('paste', function() {
        setTimeout(() => {
            autoResizeTextarea();
            updateCharacterCount();
        }, 0);
    });

    // Handle focus events
    userInput.addEventListener('focus', function() {
        const inputWrapper = document.querySelector('.input-wrapper');
        inputWrapper.classList.add('focused');
    });

    userInput.addEventListener('blur', function() {
        const inputWrapper = document.querySelector('.input-wrapper');
        inputWrapper.classList.remove('focused');
    });
}

function initializeQuickActions() {
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');

    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const prompt = this.getAttribute('data-prompt');
            if (prompt) {
                const userInput = document.getElementById('user-input');
                userInput.value = prompt;
                userInput.focus();

                // Trigger input event to update button states
                userInput.dispatchEvent(new Event('input'));

                // Auto-send the message after a short delay
                setTimeout(() => {
                    if (!isWaiting) {
                        sendMessage();
                    }
                }, 300);
            }
        });
    });
}

function updateCharacterCount() {
    const userInput = document.getElementById('user-input');
    const characterCount = document.getElementById('character-count');
    const countSpan = characterCount.querySelector('.count');

    const currentLength = userInput.value.length;
    const maxLength = parseInt(userInput.getAttribute('maxlength')) || 2000;

    countSpan.textContent = currentLength;

    // Update styling based on character count
    characterCount.classList.remove('warning', 'danger');

    if (currentLength > maxLength * 0.9) {
        characterCount.classList.add('danger');
    } else if (currentLength > maxLength * 0.75) {
        characterCount.classList.add('warning');
    }
}

function updateSendButtonState(hasText) {
    const sendBtn = document.getElementById('send-btn');
    sendBtn.disabled = !hasText || isWaiting;
    sendBtn.style.opacity = (hasText && !isWaiting) ? '1' : '0.5';
}

function autoResizeTextarea() {
    const userInput = document.getElementById('user-input');
    userInput.style.height = 'auto';
    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
}

function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const newChatBtn = document.getElementById('new-chat-btn');

    // Sidebar toggle functionality
    function toggleSidebar() {
        sidebar.classList.toggle('open');
        sidebarOverlay.classList.toggle('active');

        // Update ARIA attributes for accessibility
        const isOpen = sidebar.classList.contains('open');
        sidebar.setAttribute('aria-hidden', !isOpen);

        if (mobileSidebarToggle) {
            mobileSidebarToggle.setAttribute('aria-expanded', isOpen);
        }
    }

    function closeSidebar() {
        sidebar.classList.remove('open');
        sidebarOverlay.classList.remove('active');
        sidebar.setAttribute('aria-hidden', 'true');

        if (mobileSidebarToggle) {
            mobileSidebarToggle.setAttribute('aria-expanded', 'false');
        }
    }

    // Event listeners
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', toggleSidebar);
        mobileSidebarToggle.setAttribute('aria-expanded', 'false');
    }

    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeSidebar);
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('open')) {
            closeSidebar();
        }
    });

    // New chat functionality
    if (newChatBtn) {
        newChatBtn.addEventListener('click', function() {
            clearChat();
            showNotification('New chat started!');

            // Close sidebar on mobile after action
            if (window.innerWidth <= 767) {
                closeSidebar();
            }
        });
    }

    // Handle chat history items
    const historyItems = document.querySelectorAll('.history-item');
    historyItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Prevent action if clicking on delete button
            if (e.target.closest('.history-action')) {
                return;
            }

            // Load chat history (placeholder functionality)
            const title = item.querySelector('.history-title').textContent;
            showNotification(`Loading: ${title}`);

            // Close sidebar on mobile after action
            if (window.innerWidth <= 767) {
                closeSidebar();
            }
        });
    });

    // Handle history delete buttons
    const historyActions = document.querySelectorAll('.history-action');
    historyActions.forEach(action => {
        action.addEventListener('click', function(e) {
            e.stopPropagation();

            const historyItem = action.closest('.history-item');
            const title = historyItem.querySelector('.history-title').textContent;

            if (confirm(`Delete "${title}"?`)) {
                historyItem.remove();
                showNotification('Chat deleted');
            }
        });
    });

    // Handle navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Close sidebar on mobile after navigation
            if (window.innerWidth <= 767) {
                closeSidebar();
            }
        });
    });

    // Set initial ARIA attributes
    sidebar.setAttribute('aria-hidden', 'true');

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 767) {
            // Reset sidebar state on desktop
            sidebar.classList.remove('open');
            sidebarOverlay.classList.remove('active');
            sidebar.setAttribute('aria-hidden', 'false');
        } else {
            // Ensure sidebar is hidden on mobile by default
            if (!sidebar.classList.contains('open')) {
                sidebar.setAttribute('aria-hidden', 'true');
            }
        }
    });
}

function addMessage(message, isUser = false) {
    const messagesContainer = document.getElementById('chat-messages');
    
    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
    
    // Create avatar
    const avatar = document.createElement('div');
    avatar.className = isUser ? 'user-avatar' : 'ai-avatar';
    avatar.innerHTML = isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    // Create message bubble
    const bubble = document.createElement('div');
    bubble.className = 'message-bubble';
    
    // Handle different message formats
    if (typeof message === 'string') {
        // Check if message contains HTML-like content
        if (message.includes('\n') || message.includes('•') || message.includes('-')) {
            bubble.innerHTML = formatMessage(message);
        } else {
            bubble.textContent = message;
        }
    } else {
        bubble.textContent = String(message);
    }
    
    // Assemble message
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(bubble);
    
    // Add to container
    messagesContainer.appendChild(messageDiv);
    
    // Scroll to bottom
    scrollToBottom();
}

function formatMessage(message) {
    // Convert newlines to <br> and bullet points to proper lists
    let formatted = message
        .replace(/\n/g, '<br>')
        .replace(/•\s*/g, '<li>')
        .replace(/^\s*-\s*/gm, '<li>');
    
    // Wrap consecutive <li> elements in <ul>
    if (formatted.includes('<li>')) {
        formatted = formatted.replace(/(<li>.*?<br>)*<li>.*?(<br>|$)/g, function(match) {
            const items = match.split('<li>').filter(item => item.trim()).map(item => 
                `<li>${item.replace(/<br>/g, '').trim()}</li>`
            ).join('');
            return `<ul>${items}</ul>`;
        });
    }
    
    return formatted;
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('chat-messages');
    
    // Remove existing typing indicator
    const existingIndicator = document.getElementById('typing-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    
    // Create typing indicator
    const typingDiv = document.createElement('div');
    typingDiv.id = 'typing-indicator';
    typingDiv.className = 'typing-indicator';
    
    const avatar = document.createElement('div');
    avatar.className = 'ai-avatar';
    avatar.innerHTML = '<i class="fas fa-robot"></i>';
    
    const typingBubble = document.createElement('div');
    typingBubble.className = 'typing-bubble';
    
    const typingText = document.createElement('span');
    typingText.className = 'typing-text';
    typingText.textContent = 'Thinking';
    
    const typingDots = document.createElement('div');
    typingDots.className = 'typing-dots';
    
    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'typing-dot';
        typingDots.appendChild(dot);
    }
    
    typingBubble.appendChild(typingText);
    typingBubble.appendChild(typingDots);
    typingDiv.appendChild(avatar);
    typingDiv.appendChild(typingBubble);
    
    messagesContainer.appendChild(typingDiv);
    scrollToBottom();
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function scrollToBottom() {
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

async function sendMessage() {
    if (isWaiting) return;

    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const message = userInput.value.trim();
    
    if (!message) return;
    
    // Disable input and show loading state
    isWaiting = true;
    userInput.disabled = true;
    sendBtn.disabled = true;
    sendBtn.style.opacity = '0.5';
    
    // Add user message
    addMessage(message, true);
    userInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                message: message,
                session_id: sessionId 
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Hide typing indicator
        hideTypingIndicator();
        
        // Add AI response
        if (data.success && data.response) {
            addMessage(data.response, false);
        } else {
            addMessage('I apologize, but I encountered an issue processing your request. Please try again.', false);
        }
        
    } catch (error) {
        console.error('Error sending message:', error);
        hideTypingIndicator();
        addMessage('Sorry, I\'m having trouble connecting right now. Please check your connection and try again.', false);
    } finally {
        // Re-enable input
        isWaiting = false;
        userInput.disabled = false;
        userInput.focus();
        
        // Reset send button state
        const hasText = userInput.value.trim().length > 0;
        sendBtn.disabled = !hasText;
        sendBtn.style.opacity = hasText ? '1' : '0.5';
    }
}

function clearChat() {
    const messagesContainer = document.getElementById('chat-messages');
    
    // Show confirmation dialog
    if (confirm('Are you sure you want to clear the conversation?')) {
        // Remove all messages except welcome message
        const messages = messagesContainer.querySelectorAll('.message, .typing-indicator');
        messages.forEach(message => {
            if (!message.classList.contains('welcome-message')) {
                message.remove();
            }
        });
        
        // Generate new session ID
        sessionId = 'session_' + Date.now();
        
        // Show success feedback
        showNotification('Conversation cleared!');
    }
}

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 90px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add notification animations to CSS (via JavaScript)
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
